# Namecheap DNS API Integration

This document describes the enhanced DNS management functionality that integrates with the Namecheap API to synchronize DNS record changes with live domain configuration.

## Overview

The DNS management system now provides real-time synchronization between the local database and Namecheap's DNS servers. When users add, edit, or delete DNS records through the interface, the changes are automatically applied to the live domain configuration via the Namecheap API.

## Features

### 1. Real-time DNS Synchronization
- **Create Records**: New DNS records are immediately synced to Namecheap
- **Update Records**: Modifications are synchronized with live DNS configuration
- **Delete Records**: Removals are applied to both local database and Namecheap
- **Bidirectional Sync**: Sync from Namecheap to local or vice versa

### 2. Robust Error Handling
- **Retry Logic**: Automatic retry with exponential backoff for transient failures
- **Circuit Breaker**: Prevents cascading failures during Namecheap API outages
- **Graceful Degradation**: Local operations continue even if Namecheap sync fails
- **Detailed Error Reporting**: Clear feedback on sync status and failures

### 3. Enhanced User Interface
- **Sync Status Indicators**: Visual feedback on DNS record sync status
- **Manual Sync Controls**: Buttons to manually trigger sync operations
- **Error Messages**: Clear error reporting with actionable information
- **Last Sync Time**: Timestamp of last successful synchronization

## Architecture

### Core Components

#### 1. Namecheap DNS Service (`src/lib/namecheap-dns.ts`)
- **API Integration**: Handles all Namecheap API communications
- **Error Handling**: Implements retry logic and circuit breaker pattern
- **Data Conversion**: Converts between internal and Namecheap record formats
- **Validation**: Validates DNS records before API calls

#### 2. Enhanced API Endpoints
- **POST /api/domains/[domainId]/dns**: Creates records with Namecheap sync
- **PUT /api/domains/[domainId]/dns/[recordId]**: Updates records with sync
- **DELETE /api/domains/[domainId]/dns/[recordId]**: Deletes records with sync
- **POST /api/domains/[domainId]/dns/sync**: Manual sync operations
- **GET /api/domains/[domainId]/dns/sync**: Sync status and comparison

#### 3. Updated UI Components
- **DNSRecordsTable**: Enhanced with sync status and controls
- **DNS Management Page**: Integrated sync functionality and status monitoring

### Error Handling Strategy

#### 1. Retry Logic
```typescript
const DEFAULT_RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffMultiplier: 2
};
```

#### 2. Circuit Breaker
- **Failure Threshold**: 5 consecutive failures
- **Recovery Timeout**: 60 seconds
- **States**: CLOSED, OPEN, HALF_OPEN

#### 3. Error Types
- **NamecheapAPIError**: General API errors
- **NamecheapRateLimitError**: Rate limiting (retryable)
- **NamecheapNetworkError**: Network issues (retryable)

## API Reference

### Namecheap DNS Service Functions

#### `getNamecheapDNSRecords(domain: string)`
Fetches current DNS records from Namecheap for the specified domain.

**Returns**: `Promise<NamecheapDNSRecord[]>`

#### `setNamecheapDNSRecords(domain: string, records: DNSRecord[])`
Updates all DNS records for a domain on Namecheap.

**Parameters**:
- `domain`: Domain name (e.g., "example.com")
- `records`: Array of DNS records to set

**Returns**: `Promise<{ success: boolean; message: string }>`

#### `validateDNSRecord(record: DNSRecord)`
Validates a DNS record before API submission.

**Returns**: `{ valid: boolean; errors: string[] }`

### Sync API Endpoints

#### `POST /api/domains/[domainId]/dns/sync`
Performs manual synchronization between local database and Namecheap.

**Request Body**:
```json
{
  "direction": "from_namecheap" | "to_namecheap"
}
```

**Response**:
```json
{
  "success": boolean,
  "message": string,
  "details": {
    "namecheapRecords": number,
    "localRecords": number,
    "added": number,
    "updated": number,
    "removed": number,
    "errors": string[]
  }
}
```

#### `GET /api/domains/[domainId]/dns/sync`
Returns sync status and comparison between local and Namecheap records.

**Response**:
```json
{
  "inSync": boolean,
  "namecheapRecords": NamecheapDNSRecord[],
  "localRecords": DNSRecord[],
  "summary": {
    "namecheapCount": number,
    "localCount": number,
    "pendingRecords": number,
    "errorRecords": number
  }
}
```

## Configuration

### Environment Variables

```bash
# Namecheap API Configuration
NAMECHEAP_API_USER=your-api-user
NAMECHEAP_API_KEY=your-api-key
NAMECHEAP_USERNAME=your-username
NAMECHEAP_CLIENT_IP=your-whitelisted-ip
NAMECHEAP_SANDBOX=true  # Set to false for production
```

### DNS Record Status Values

- **`active`**: Record is synced and active on Namecheap
- **`pending`**: Record is being processed/synced
- **`error`**: Record failed to sync with Namecheap

## Usage Examples

### Creating a DNS Record
```typescript
const newRecord = {
  type: 'A',
  name: 'www',
  value: '***********',
  ttl: 3600
};

// Record is automatically synced to Namecheap
const response = await fetch(`/api/domains/${domainId}/dns`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(newRecord)
});
```

### Manual Sync Operations
```typescript
// Sync from Namecheap to local database
await fetch(`/api/domains/${domainId}/dns/sync`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ direction: 'from_namecheap' })
});

// Sync from local database to Namecheap
await fetch(`/api/domains/${domainId}/dns/sync`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ direction: 'to_namecheap' })
});
```

## Testing

### Running Tests
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test namecheap-dns.test.ts
```

### Test Coverage
- Unit tests for Namecheap DNS service functions
- Integration tests for API endpoints
- Error handling and retry logic tests
- Circuit breaker functionality tests

## Monitoring and Troubleshooting

### Circuit Breaker Status
```typescript
import { getCircuitBreakerStatus } from '@/lib/namecheap-dns';

const status = getCircuitBreakerStatus();
console.log('Circuit breaker state:', status.state);
console.log('Failure count:', status.failures);
```

### Common Issues

1. **Rate Limiting**: Namecheap has API rate limits. The system automatically retries with backoff.

2. **Network Timeouts**: Requests timeout after 30 seconds with automatic retry.

3. **Invalid Records**: Records are validated before API submission to prevent errors.

4. **Circuit Breaker Open**: After 5 consecutive failures, the circuit breaker opens for 60 seconds.

### Logging
All API interactions are logged with appropriate detail levels:
- Info: Successful operations
- Warning: Retryable errors
- Error: Non-retryable failures

## Security Considerations

1. **API Credentials**: Store Namecheap API credentials securely in environment variables
2. **IP Whitelisting**: Ensure your server IP is whitelisted in Namecheap API settings
3. **Input Validation**: All DNS records are validated before API submission
4. **Rate Limiting**: Respect Namecheap API rate limits to avoid account suspension

## Future Enhancements

1. **Bulk Operations**: Support for bulk DNS record operations
2. **Change History**: Track and display DNS record change history
3. **Automated Backups**: Regular backups of DNS configurations
4. **Multi-Provider Support**: Support for additional DNS providers
5. **Advanced Monitoring**: Detailed metrics and alerting for DNS operations
