import '@testing-library/jest-dom'

// Mock environment variables for tests
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co'
process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key'
process.env.NAMECHEAP_API_USER = 'test-user'
process.env.NAMECHEAP_API_KEY = 'test-key'
process.env.NAMECHEAP_USERNAME = 'test-username'
process.env.NAMECHEAP_CLIENT_IP = '***********'
process.env.NAMECHEAP_SANDBOX = 'true'

// Mock fetch globally
global.fetch = jest.fn()

// Reset mocks before each test
beforeEach(() => {
  jest.clearAllMocks()
})

// Suppress console.log during tests unless explicitly needed
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}
