import React, { useEffect, useRef } from 'react';
import { X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface DocsBotModalProps {
  isOpen: boolean;
  onClose: () => void;
  question?: string | null;
}

const DOCSBOT_URL = 'https://docsbot.ai/iframe/YUbbrN66lKFxqOW59Jp7/4MumvWvdkKhm6BvdcrnX';

const DocsBotModal: React.FC<DocsBotModalProps> = ({ isOpen, onClose, question }) => {
  const backdropRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Post the question to DocsBot iframe whenever the modal opens or the question updates.
  useEffect(() => {
    if (!isOpen || !question) return;

    const iframe = iframeRef.current;
    if (!iframe) return; // iframe not yet rendered

    const postQuestion = () => {
      try {
        iframe.contentWindow?.postMessage({ question }, '*');
      } catch (err) {
        console.error('Failed to post question to DocsBot iframe', err);
      }
    };

    // If iframe already loaded, send immediately; otherwise wait for load event
    if (iframe.contentDocument?.readyState === 'complete') {
      postQuestion();
    } else {
      iframe.addEventListener('load', postQuestion, { once: true });
      return () => iframe.removeEventListener('load', postQuestion);
    }
  }, [isOpen, question]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === backdropRef.current) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={backdropRef}
          onClick={handleBackdropClick}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0, y: 40 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.95, opacity: 0, y: 20 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="relative w-full h-full sm:max-w-3xl sm:h-[80vh] bg-white rounded-2xl shadow-2xl overflow-hidden"
          >
            <button
              onClick={onClose}
              className="absolute flex items-center justify-center w-10 h-10 transition bg-white rounded-full shadow-md top-2 right-2 hover:bg-gray-100"
              aria-label="Close modal"
            >
              <X className="w-5 h-5 text-gray-600" />
            </button>
            <iframe
              ref={iframeRef}
              src={DOCSBOT_URL}
              width="100%"
              height="100%"
              style={{ border: 'none', minHeight: '70vh' }}
              allow="clipboard-write"
              title="DocsBot Help"
            />
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default DocsBotModal; 