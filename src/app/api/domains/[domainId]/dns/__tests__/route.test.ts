import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { POST, GET } from '../route';
import * as namecheapDNS from '../../../../../lib/namecheap-dns';

// Mock the Namecheap DNS module
jest.mock('../../../../../lib/namecheap-dns');

// Mock Supabase
const mockSupabaseAdmin = {
  auth: {
    getUser: jest.fn()
  },
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        single: jest.fn(),
        order: jest.fn(() => ({
          eq: jest.fn()
        }))
      }))
    })),
    insert: jest.fn(() => ({
      select: jest.fn(() => ({
        single: jest.fn()
      }))
    })),
    update: jest.fn(() => ({
      eq: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn()
        }))
      }))
    }))
  }))
};

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabaseAdmin)
}));

describe('DNS API Routes', () => {
  const mockUser = { id: 'user-123' };
  const mockDomain = { id: 'domain-123', domain_name: 'example.com' };
  const mockDNSRecord = {
    id: 'record-123',
    domain_id: 'domain-123',
    type: 'A',
    name: 'www',
    value: '192.168.1.1',
    ttl: 3600,
    status: 'active',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful authentication
    mockSupabaseAdmin.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('GET /api/domains/[domainId]/dns', () => {
    it('should fetch DNS records successfully', async () => {
      // Mock domain verification
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({
              data: mockDomain,
              error: null
            })
          }))
        }))
      });

      // Mock DNS records fetch
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn().mockResolvedValue({
              data: [mockDNSRecord],
              error: null
            })
          }))
        }))
      });

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns', {
        headers: { authorization: 'Bearer valid-token' }
      });

      const response = await GET(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.records).toHaveLength(1);
      expect(data.records[0]).toEqual(mockDNSRecord);
    });

    it('should return 401 for unauthenticated requests', async () => {
      const request = new NextRequest('http://localhost/api/domains/domain-123/dns');

      const response = await GET(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Not authenticated');
    });

    it('should return 404 for non-existent domain', async () => {
      // Mock domain not found
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Not found' }
            })
          }))
        }))
      });

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns', {
        headers: { authorization: 'Bearer valid-token' }
      });

      const response = await GET(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('Domain not found or access denied');
    });
  });

  describe('POST /api/domains/[domainId]/dns', () => {
    const newRecord = {
      type: 'A',
      name: 'test',
      value: '***********',
      ttl: 3600,
      status: 'pending'
    };

    it('should create DNS record and sync with Namecheap successfully', async () => {
      // Mock domain verification
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({
              data: mockDomain,
              error: null
            })
          }))
        }))
      });

      // Mock record creation
      mockSupabaseAdmin.from.mockReturnValueOnce({
        insert: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({
              data: { ...mockDNSRecord, ...newRecord },
              error: null
            })
          }))
        }))
      });

      // Mock fetching all records for sync
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({
            data: [{ ...mockDNSRecord, ...newRecord }],
            error: null
          })
        }))
      });

      // Mock record status update
      mockSupabaseAdmin.from.mockReturnValueOnce({
        update: jest.fn(() => ({
          eq: jest.fn(() => ({
            select: jest.fn(() => ({
              single: jest.fn().mockResolvedValue({
                data: { ...mockDNSRecord, ...newRecord, status: 'active' },
                error: null
              })
            }))
          }))
        }))
      });

      // Mock Namecheap API calls
      (namecheapDNS.validateDNSRecord as jest.Mock).mockReturnValue({
        valid: true,
        errors: []
      });
      (namecheapDNS.setNamecheapDNSRecords as jest.Mock).mockResolvedValue({
        success: true,
        message: 'DNS records updated successfully'
      });

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns', {
        method: 'POST',
        headers: {
          'authorization': 'Bearer valid-token',
          'content-type': 'application/json'
        },
        body: JSON.stringify(newRecord)
      });

      const response = await POST(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.record).toBeDefined();
      expect(data.message).toContain('synced with Namecheap successfully');
      expect(namecheapDNS.setNamecheapDNSRecords).toHaveBeenCalledWith(
        'example.com',
        expect.arrayContaining([expect.objectContaining(newRecord)])
      );
    });

    it('should handle Namecheap sync failure gracefully', async () => {
      // Mock domain verification
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({
              data: mockDomain,
              error: null
            })
          }))
        }))
      });

      // Mock record creation
      mockSupabaseAdmin.from.mockReturnValueOnce({
        insert: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({
              data: { ...mockDNSRecord, ...newRecord },
              error: null
            })
          }))
        }))
      });

      // Mock fetching all records for sync
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({
            data: [{ ...mockDNSRecord, ...newRecord }],
            error: null
          })
        }))
      });

      // Mock record status update to error
      mockSupabaseAdmin.from.mockReturnValueOnce({
        update: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({
            data: null,
            error: null
          })
        }))
      });

      // Mock Namecheap API calls
      (namecheapDNS.validateDNSRecord as jest.Mock).mockReturnValue({
        valid: true,
        errors: []
      });
      (namecheapDNS.setNamecheapDNSRecords as jest.Mock).mockRejectedValue(
        new Error('Namecheap API error')
      );

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns', {
        method: 'POST',
        headers: {
          'authorization': 'Bearer valid-token',
          'content-type': 'application/json'
        },
        body: JSON.stringify(newRecord)
      });

      const response = await POST(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(207); // Multi-Status
      expect(data.record).toBeDefined();
      expect(data.error).toContain('failed to sync with Namecheap');
      expect(data.warning).toBeDefined();
    });

    it('should validate DNS record before creation', async () => {
      const invalidRecord = {
        type: 'MX',
        name: 'mail',
        value: 'mail.example.com',
        ttl: 3600
        // Missing priority for MX record
      };

      // Mock domain verification
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({
              data: mockDomain,
              error: null
            })
          }))
        }))
      });

      // Mock validation failure
      (namecheapDNS.validateDNSRecord as jest.Mock).mockReturnValue({
        valid: false,
        errors: ['MX records must have a priority between 0 and 65535']
      });

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns', {
        method: 'POST',
        headers: {
          'authorization': 'Bearer valid-token',
          'content-type': 'application/json'
        },
        body: JSON.stringify(invalidRecord)
      });

      const response = await POST(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid DNS record');
      expect(data.details).toContain('MX records must have a priority between 0 and 65535');
    });

    it('should handle missing required fields', async () => {
      const incompleteRecord = {
        type: 'A',
        // Missing name, value, ttl
      };

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns', {
        method: 'POST',
        headers: {
          'authorization': 'Bearer valid-token',
          'content-type': 'application/json'
        },
        body: JSON.stringify(incompleteRecord)
      });

      const response = await POST(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('required');
    });
  });
});
