import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { getNamecheapDNSRecords, setNamecheapDNSRecords, validateDNSRecord, type DNSRecord as NamecheapDNSRecord } from '../../../../../lib/namecheap-dns';

// Initialize Supabase client with service role key for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface DNSRecord {
  id: string;
  domain_id: string;
  type: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'NS' | 'PTR' | 'SRV' | 'CAA';
  name: string;
  value: string;
  ttl: number;
  priority?: number; // For MX records
  status: 'active' | 'pending' | 'error';
  created_at: string;
  updated_at: string;
}

// PUT /api/domains/[domainId]/dns/[recordId] - Update a DNS record
export async function PUT(
  request: NextRequest,
  { params }: { params: { domainId: string; recordId: string } }
) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const { domainId, recordId } = params;

    if (!domainId || !recordId) {
      return NextResponse.json({ error: 'Domain ID and Record ID are required' }, { status: 400 });
    }

    // First, verify the user owns this domain
    const { data: domain, error: domainError } = await supabaseAdmin
      .from('domains')
      .select('id')
      .eq('id', domainId)
      .eq('user_id', user.id)
      .single();

    if (domainError || !domain) {
      return NextResponse.json({ error: 'Domain not found or access denied' }, { status: 404 });
    }

    // Verify the DNS record exists and belongs to this domain
    const { data: existingRecord, error: recordError } = await supabaseAdmin
      .from('dns_records')
      .select('*')
      .eq('id', recordId)
      .eq('domain_id', domainId)
      .single();

    if (recordError || !existingRecord) {
      return NextResponse.json({ error: 'DNS record not found' }, { status: 404 });
    }

    const body = await request.json();
    const {
      type,
      name,
      value,
      ttl,
      priority,
      status
    } = body;

    // Validate required fields
    if (!type || !name || !value) {
      return NextResponse.json({ error: 'Type, name, and value are required' }, { status: 400 });
    }

    // Validate DNS record type
    const validTypes = ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS', 'PTR', 'SRV', 'CAA'];
    if (!validTypes.includes(type)) {
      return NextResponse.json({ error: 'Invalid DNS record type' }, { status: 400 });
    }

    // Validate TTL
    if (ttl && (ttl < 60 || ttl > 604800)) {
      return NextResponse.json({ error: 'TTL must be between 60 and 604800 seconds' }, { status: 400 });
    }

    // Validate priority for MX records
    if (type === 'MX' && (priority === undefined || priority < 0 || priority > 65535)) {
      return NextResponse.json({ error: 'Priority is required for MX records and must be between 0 and 65535' }, { status: 400 });
    }

    // Get domain name for Namecheap API
    const { data: domainData, error: domainFetchError } = await supabaseAdmin
      .from('domains')
      .select('domain_name')
      .eq('id', domainId)
      .single();

    if (domainFetchError || !domainData) {
      return NextResponse.json({ error: 'Domain not found' }, { status: 404 });
    }

    // Create updated DNS record object for validation
    const updatedRecord: NamecheapDNSRecord = {
      type,
      name: name.trim(),
      value: value.trim(),
      ttl: ttl !== undefined ? ttl : existingRecord.ttl,
      priority: type === 'MX' ? priority : undefined,
      status: 'pending' // Set to pending during update
    };

    // Validate the updated DNS record
    const validation = validateDNSRecord(updatedRecord);
    if (!validation.valid) {
      return NextResponse.json({
        error: 'Invalid DNS record',
        details: validation.errors
      }, { status: 400 });
    }

    // Update the DNS record in database with pending status
    const updateData: Partial<DNSRecord> = {
      type,
      name: name.trim(),
      value: value.trim(),
      updated_at: new Date().toISOString(),
      status: 'pending'
    };

    if (ttl !== undefined) updateData.ttl = ttl;
    if (priority !== undefined) updateData.priority = type === 'MX' ? priority : null;

    const { data: record, error } = await supabaseAdmin
      .from('dns_records')
      .update(updateData)
      .eq('id', recordId)
      .eq('domain_id', domainId)
      .select()
      .single();

    if (error) {
      console.error('Error updating DNS record:', error);
      return NextResponse.json({ error: 'Failed to update DNS record' }, { status: 500 });
    }

    // Now sync with Namecheap API
    try {
      // Get all DNS records from our database for this domain
      const { data: allLocalRecords, error: fetchError } = await supabaseAdmin
        .from('dns_records')
        .select('*')
        .eq('domain_id', domainId);

      if (fetchError) {
        throw new Error('Failed to fetch local DNS records');
      }

      // Convert local records to Namecheap format
      const allRecordsForNamecheap: NamecheapDNSRecord[] = allLocalRecords.map(r => ({
        type: r.type,
        name: r.name,
        value: r.value,
        ttl: r.ttl,
        priority: r.priority || undefined
      }));

      // Update all DNS records on Namecheap
      await setNamecheapDNSRecords(domainData.domain_name, allRecordsForNamecheap);

      // Update the record status to active since Namecheap sync succeeded
      const { data: updatedRecord, error: updateError } = await supabaseAdmin
        .from('dns_records')
        .update({ status: 'active' })
        .eq('id', record.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating DNS record status:', updateError);
        // Don't fail the request since the record was updated and synced successfully
      }

      return NextResponse.json({
        record: updatedRecord || record,
        message: 'DNS record updated and synced with Namecheap successfully'
      });

    } catch (namecheapError: any) {
      console.error('Error syncing with Namecheap:', namecheapError);

      // Update the record status to error
      await supabaseAdmin
        .from('dns_records')
        .update({ status: 'error' })
        .eq('id', record.id);

      return NextResponse.json({
        record,
        error: 'DNS record updated locally but failed to sync with Namecheap',
        details: namecheapError.message,
        warning: 'The record exists in the database but may not be active on the domain until manually synced.'
      }, { status: 207 }); // 207 Multi-Status for partial success
    }
  } catch (error: any) {
    console.error('DNS record update error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/domains/[domainId]/dns/[recordId] - Delete a DNS record
export async function DELETE(
  request: NextRequest,
  { params }: { params: { domainId: string; recordId: string } }
) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const { domainId, recordId } = params;

    if (!domainId || !recordId) {
      return NextResponse.json({ error: 'Domain ID and Record ID are required' }, { status: 400 });
    }

    // First, verify the user owns this domain and get domain name
    const { data: domain, error: domainError } = await supabaseAdmin
      .from('domains')
      .select('id, domain_name')
      .eq('id', domainId)
      .eq('user_id', user.id)
      .single();

    if (domainError || !domain) {
      return NextResponse.json({ error: 'Domain not found or access denied' }, { status: 404 });
    }

    // Verify the DNS record exists and belongs to this domain
    const { data: existingRecord, error: recordError } = await supabaseAdmin
      .from('dns_records')
      .select('*')
      .eq('id', recordId)
      .eq('domain_id', domainId)
      .single();

    if (recordError || !existingRecord) {
      return NextResponse.json({ error: 'DNS record not found' }, { status: 404 });
    }

    // Delete the DNS record from database
    const { error } = await supabaseAdmin
      .from('dns_records')
      .delete()
      .eq('id', recordId)
      .eq('domain_id', domainId);

    if (error) {
      console.error('Error deleting DNS record:', error);
      return NextResponse.json({ error: 'Failed to delete DNS record' }, { status: 500 });
    }

    // Now sync with Namecheap API
    try {
      // Get all remaining DNS records from our database for this domain
      const { data: remainingRecords, error: fetchError } = await supabaseAdmin
        .from('dns_records')
        .select('*')
        .eq('domain_id', domainId);

      if (fetchError) {
        throw new Error('Failed to fetch remaining DNS records');
      }

      // Convert remaining records to Namecheap format
      const recordsForNamecheap: NamecheapDNSRecord[] = remainingRecords.map(r => ({
        type: r.type,
        name: r.name,
        value: r.value,
        ttl: r.ttl,
        priority: r.priority || undefined
      }));

      // If no records remain, we need to set at least one record (Namecheap requirement)
      // Set a default A record pointing to a parking page or similar
      if (recordsForNamecheap.length === 0) {
        recordsForNamecheap.push({
          type: 'A',
          name: '@',
          value: '*********', // RFC 5737 documentation IP
          ttl: 3600
        });
      }

      // Update DNS records on Namecheap
      await setNamecheapDNSRecords(domain.domain_name, recordsForNamecheap);

      return NextResponse.json({
        message: 'DNS record deleted and synced with Namecheap successfully'
      });

    } catch (namecheapError: any) {
      console.error('Error syncing deletion with Namecheap:', namecheapError);

      return NextResponse.json({
        message: 'DNS record deleted locally but failed to sync with Namecheap',
        error: namecheapError.message,
        warning: 'The record was removed from the database but may still be active on the domain until manually synced.'
      }, { status: 207 }); // 207 Multi-Status for partial success
    }
  } catch (error: any) {
    console.error('DNS record deletion error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 