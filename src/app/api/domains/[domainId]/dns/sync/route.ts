import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { getNamecheapDNSRecords, setNamecheapDNSRecords, convertNamecheapToInternal, type DNSRecord } from '../../../../../lib/namecheap-dns';

// Initialize Supabase client with service role key for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface SyncResult {
  success: boolean;
  message: string;
  details: {
    namecheapRecords: number;
    localRecords: number;
    added: number;
    updated: number;
    removed: number;
    errors: string[];
  };
}

// POST /api/domains/[domainId]/dns/sync - Sync DNS records with Namecheap
export async function POST(
  request: NextRequest,
  { params }: { params: { domainId: string } }
) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const { domainId } = params;

    if (!domainId) {
      return NextResponse.json({ error: 'Domain ID is required' }, { status: 400 });
    }

    // Get the request body to determine sync direction
    const body = await request.json();
    const { direction = 'from_namecheap' } = body; // 'from_namecheap' or 'to_namecheap'

    // Verify the user owns this domain and get domain name
    const { data: domain, error: domainError } = await supabaseAdmin
      .from('domains')
      .select('id, domain_name')
      .eq('id', domainId)
      .eq('user_id', user.id)
      .single();

    if (domainError || !domain) {
      return NextResponse.json({ error: 'Domain not found or access denied' }, { status: 404 });
    }

    const syncResult: SyncResult = {
      success: false,
      message: '',
      details: {
        namecheapRecords: 0,
        localRecords: 0,
        added: 0,
        updated: 0,
        removed: 0,
        errors: []
      }
    };

    if (direction === 'from_namecheap') {
      // Sync FROM Namecheap TO local database
      try {
        // Get current DNS records from Namecheap
        const namecheapRecords = await getNamecheapDNSRecords(domain.domain_name);
        syncResult.details.namecheapRecords = namecheapRecords.length;

        // Get current local DNS records
        const { data: localRecords, error: fetchError } = await supabaseAdmin
          .from('dns_records')
          .select('*')
          .eq('domain_id', domainId);

        if (fetchError) {
          throw new Error('Failed to fetch local DNS records');
        }

        syncResult.details.localRecords = localRecords.length;

        // Convert Namecheap records to our internal format
        const convertedRecords = namecheapRecords.map(convertNamecheapToInternal);

        // Clear existing local records
        const { error: deleteError } = await supabaseAdmin
          .from('dns_records')
          .delete()
          .eq('domain_id', domainId);

        if (deleteError) {
          throw new Error('Failed to clear existing DNS records');
        }

        syncResult.details.removed = localRecords.length;

        // Insert new records from Namecheap
        if (convertedRecords.length > 0) {
          const recordsToInsert = convertedRecords.map(record => ({
            domain_id: domainId,
            type: record.type,
            name: record.name,
            value: record.value,
            ttl: record.ttl,
            priority: record.priority || null,
            status: 'active'
          }));

          const { error: insertError } = await supabaseAdmin
            .from('dns_records')
            .insert(recordsToInsert);

          if (insertError) {
            throw new Error('Failed to insert DNS records from Namecheap');
          }

          syncResult.details.added = convertedRecords.length;
        }

        syncResult.success = true;
        syncResult.message = `Successfully synced ${convertedRecords.length} DNS records from Namecheap to local database`;

      } catch (error: any) {
        syncResult.details.errors.push(error.message);
        syncResult.message = 'Failed to sync DNS records from Namecheap';
      }

    } else if (direction === 'to_namecheap') {
      // Sync FROM local database TO Namecheap
      try {
        // Get current local DNS records
        const { data: localRecords, error: fetchError } = await supabaseAdmin
          .from('dns_records')
          .select('*')
          .eq('domain_id', domainId);

        if (fetchError) {
          throw new Error('Failed to fetch local DNS records');
        }

        syncResult.details.localRecords = localRecords.length;

        // Convert local records to Namecheap format
        const recordsForNamecheap: DNSRecord[] = localRecords.map(r => ({
          type: r.type,
          name: r.name,
          value: r.value,
          ttl: r.ttl,
          priority: r.priority || undefined
        }));

        // If no records exist locally, set a default record
        if (recordsForNamecheap.length === 0) {
          recordsForNamecheap.push({
            type: 'A',
            name: '@',
            value: '*********', // RFC 5737 documentation IP
            ttl: 3600
          });
        }

        // Update DNS records on Namecheap
        await setNamecheapDNSRecords(domain.domain_name, recordsForNamecheap);

        // Update all local records to active status
        await supabaseAdmin
          .from('dns_records')
          .update({ status: 'active' })
          .eq('domain_id', domainId);

        syncResult.details.updated = localRecords.length;
        syncResult.success = true;
        syncResult.message = `Successfully synced ${localRecords.length} DNS records from local database to Namecheap`;

      } catch (error: any) {
        // Update local records to error status
        await supabaseAdmin
          .from('dns_records')
          .update({ status: 'error' })
          .eq('domain_id', domainId);

        syncResult.details.errors.push(error.message);
        syncResult.message = 'Failed to sync DNS records to Namecheap';
      }

    } else {
      return NextResponse.json({ error: 'Invalid sync direction. Use "from_namecheap" or "to_namecheap"' }, { status: 400 });
    }

    const statusCode = syncResult.success ? 200 : 500;
    return NextResponse.json(syncResult, { status: statusCode });

  } catch (error: any) {
    console.error('DNS sync error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET /api/domains/[domainId]/dns/sync - Get sync status and comparison
export async function GET(
  request: NextRequest,
  { params }: { params: { domainId: string } }
) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const { domainId } = params;

    if (!domainId) {
      return NextResponse.json({ error: 'Domain ID is required' }, { status: 400 });
    }

    // Verify the user owns this domain and get domain name
    const { data: domain, error: domainError } = await supabaseAdmin
      .from('domains')
      .select('id, domain_name')
      .eq('id', domainId)
      .eq('user_id', user.id)
      .single();

    if (domainError || !domain) {
      return NextResponse.json({ error: 'Domain not found or access denied' }, { status: 404 });
    }

    try {
      // Get DNS records from both sources
      const [namecheapRecords, { data: localRecords }] = await Promise.all([
        getNamecheapDNSRecords(domain.domain_name),
        supabaseAdmin
          .from('dns_records')
          .select('*')
          .eq('domain_id', domainId)
      ]);

      // Convert Namecheap records for comparison
      const convertedNamecheapRecords = namecheapRecords.map(convertNamecheapToInternal);

      // Compare records to determine sync status
      const inSync = localRecords.length === convertedNamecheapRecords.length &&
        localRecords.every(local => 
          convertedNamecheapRecords.some(namecheap => 
            namecheap.type === local.type &&
            namecheap.name === local.name &&
            namecheap.value === local.value &&
            namecheap.ttl === local.ttl &&
            namecheap.priority === local.priority
          )
        );

      return NextResponse.json({
        inSync,
        namecheapRecords: convertedNamecheapRecords,
        localRecords,
        summary: {
          namecheapCount: namecheapRecords.length,
          localCount: localRecords.length,
          pendingRecords: localRecords.filter(r => r.status === 'pending').length,
          errorRecords: localRecords.filter(r => r.status === 'error').length
        }
      });

    } catch (error: any) {
      console.error('Error comparing DNS records:', error);
      return NextResponse.json({ 
        error: 'Failed to compare DNS records',
        details: error.message 
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('DNS sync status error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
