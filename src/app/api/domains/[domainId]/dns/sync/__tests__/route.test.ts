import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { POST, GET } from '../route';
import * as namecheapDNS from '../../../../../../lib/namecheap-dns';

// Mock the Namecheap DNS module
jest.mock('../../../../../../lib/namecheap-dns');

// Mock Supabase
const mockSupabaseAdmin = {
  auth: {
    getUser: jest.fn()
  },
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        single: jest.fn()
      }))
    })),
    delete: jest.fn(() => ({
      eq: jest.fn()
    })),
    insert: jest.fn(),
    update: jest.fn(() => ({
      eq: jest.fn()
    }))
  }))
};

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabaseAdmin)
}));

describe('DNS Sync API Routes', () => {
  const mockUser = { id: 'user-123' };
  const mockDomain = { id: 'domain-123', domain_name: 'example.com' };
  const mockLocalRecords = [
    {
      id: 'record-1',
      domain_id: 'domain-123',
      type: 'A',
      name: 'www',
      value: '***********',
      ttl: 3600,
      status: 'active'
    },
    {
      id: 'record-2',
      domain_id: 'domain-123',
      type: 'MX',
      name: '@',
      value: 'mail.example.com',
      ttl: 3600,
      priority: 10,
      status: 'active'
    }
  ];

  const mockNamecheapRecords = [
    {
      HostId: '1',
      HostName: 'www',
      RecordType: 'A',
      Address: '***********',
      TTL: '3600'
    },
    {
      HostId: '2',
      HostName: '@',
      RecordType: 'MX',
      Address: 'mail.example.com',
      TTL: '3600',
      MXPref: '10'
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful authentication
    mockSupabaseAdmin.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null
    });

    // Mock domain verification
    mockSupabaseAdmin.from.mockReturnValue({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn().mockResolvedValue({
            data: mockDomain,
            error: null
          })
        }))
      }))
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('GET /api/domains/[domainId]/dns/sync', () => {
    it('should return sync status when records are in sync', async () => {
      // Mock Namecheap records fetch
      (namecheapDNS.getNamecheapDNSRecords as jest.Mock).mockResolvedValue(mockNamecheapRecords);
      (namecheapDNS.convertNamecheapToInternal as jest.Mock)
        .mockReturnValueOnce({
          type: 'A',
          name: 'www',
          value: '***********',
          ttl: 3600,
          status: 'active'
        })
        .mockReturnValueOnce({
          type: 'MX',
          name: '@',
          value: 'mail.example.com',
          ttl: 3600,
          priority: 10,
          status: 'active'
        });

      // Mock local records fetch
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({
            data: mockLocalRecords,
            error: null
          })
        }))
      });

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns/sync', {
        headers: { authorization: 'Bearer valid-token' }
      });

      const response = await GET(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.inSync).toBe(true);
      expect(data.summary.namecheapCount).toBe(2);
      expect(data.summary.localCount).toBe(2);
    });

    it('should return sync status when records are out of sync', async () => {
      // Mock Namecheap records with different data
      const differentNamecheapRecords = [
        {
          HostId: '1',
          HostName: 'www',
          RecordType: 'A',
          Address: '***********', // Different IP
          TTL: '3600'
        }
      ];

      (namecheapDNS.getNamecheapDNSRecords as jest.Mock).mockResolvedValue(differentNamecheapRecords);
      (namecheapDNS.convertNamecheapToInternal as jest.Mock).mockReturnValue({
        type: 'A',
        name: 'www',
        value: '***********',
        ttl: 3600,
        status: 'active'
      });

      // Mock local records fetch
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({
            data: mockLocalRecords,
            error: null
          })
        }))
      });

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns/sync', {
        headers: { authorization: 'Bearer valid-token' }
      });

      const response = await GET(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.inSync).toBe(false);
      expect(data.summary.namecheapCount).toBe(1);
      expect(data.summary.localCount).toBe(2);
    });

    it('should handle Namecheap API errors', async () => {
      (namecheapDNS.getNamecheapDNSRecords as jest.Mock).mockRejectedValue(
        new Error('Namecheap API error')
      );

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns/sync', {
        headers: { authorization: 'Bearer valid-token' }
      });

      const response = await GET(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to compare DNS records');
      expect(data.details).toBe('Namecheap API error');
    });
  });

  describe('POST /api/domains/[domainId]/dns/sync', () => {
    it('should sync from Namecheap to local database successfully', async () => {
      // Mock Namecheap records fetch
      (namecheapDNS.getNamecheapDNSRecords as jest.Mock).mockResolvedValue(mockNamecheapRecords);
      (namecheapDNS.convertNamecheapToInternal as jest.Mock)
        .mockReturnValueOnce({
          type: 'A',
          name: 'www',
          value: '***********',
          ttl: 3600,
          status: 'active'
        })
        .mockReturnValueOnce({
          type: 'MX',
          name: '@',
          value: 'mail.example.com',
          ttl: 3600,
          priority: 10,
          status: 'active'
        });

      // Mock local records fetch
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({
            data: mockLocalRecords,
            error: null
          })
        }))
      });

      // Mock delete operation
      mockSupabaseAdmin.from.mockReturnValueOnce({
        delete: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({
            error: null
          })
        }))
      });

      // Mock insert operation
      mockSupabaseAdmin.from.mockReturnValueOnce({
        insert: jest.fn().mockResolvedValue({
          error: null
        })
      });

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns/sync', {
        method: 'POST',
        headers: {
          'authorization': 'Bearer valid-token',
          'content-type': 'application/json'
        },
        body: JSON.stringify({ direction: 'from_namecheap' })
      });

      const response = await POST(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.message).toContain('synced 2 DNS records from Namecheap');
      expect(data.details.added).toBe(2);
      expect(data.details.removed).toBe(2);
    });

    it('should sync from local database to Namecheap successfully', async () => {
      // Mock local records fetch
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({
            data: mockLocalRecords,
            error: null
          })
        }))
      });

      // Mock Namecheap API call
      (namecheapDNS.setNamecheapDNSRecords as jest.Mock).mockResolvedValue({
        success: true,
        message: 'DNS records updated successfully'
      });

      // Mock status update
      mockSupabaseAdmin.from.mockReturnValueOnce({
        update: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({
            error: null
          })
        }))
      });

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns/sync', {
        method: 'POST',
        headers: {
          'authorization': 'Bearer valid-token',
          'content-type': 'application/json'
        },
        body: JSON.stringify({ direction: 'to_namecheap' })
      });

      const response = await POST(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.message).toContain('synced 2 DNS records from local database to Namecheap');
      expect(data.details.updated).toBe(2);
      expect(namecheapDNS.setNamecheapDNSRecords).toHaveBeenCalledWith(
        'example.com',
        expect.arrayContaining([
          expect.objectContaining({ type: 'A', name: 'www' }),
          expect.objectContaining({ type: 'MX', name: '@' })
        ])
      );
    });

    it('should handle empty local records by setting default record', async () => {
      // Mock empty local records
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({
            data: [],
            error: null
          })
        }))
      });

      // Mock Namecheap API call
      (namecheapDNS.setNamecheapDNSRecords as jest.Mock).mockResolvedValue({
        success: true,
        message: 'DNS records updated successfully'
      });

      // Mock status update
      mockSupabaseAdmin.from.mockReturnValueOnce({
        update: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({
            error: null
          })
        }))
      });

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns/sync', {
        method: 'POST',
        headers: {
          'authorization': 'Bearer valid-token',
          'content-type': 'application/json'
        },
        body: JSON.stringify({ direction: 'to_namecheap' })
      });

      const response = await POST(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(namecheapDNS.setNamecheapDNSRecords).toHaveBeenCalledWith(
        'example.com',
        expect.arrayContaining([
          expect.objectContaining({
            type: 'A',
            name: '@',
            value: '*********',
            ttl: 3600
          })
        ])
      );
    });

    it('should handle Namecheap API failures', async () => {
      // Mock local records fetch
      mockSupabaseAdmin.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({
            data: mockLocalRecords,
            error: null
          })
        }))
      });

      // Mock Namecheap API failure
      (namecheapDNS.setNamecheapDNSRecords as jest.Mock).mockRejectedValue(
        new Error('Namecheap API error')
      );

      // Mock status update to error
      mockSupabaseAdmin.from.mockReturnValueOnce({
        update: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({
            error: null
          })
        }))
      });

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns/sync', {
        method: 'POST',
        headers: {
          'authorization': 'Bearer valid-token',
          'content-type': 'application/json'
        },
        body: JSON.stringify({ direction: 'to_namecheap' })
      });

      const response = await POST(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.message).toBe('Failed to sync DNS records to Namecheap');
      expect(data.details.errors).toContain('Namecheap API error');
    });

    it('should validate sync direction', async () => {
      const request = new NextRequest('http://localhost/api/domains/domain-123/dns/sync', {
        method: 'POST',
        headers: {
          'authorization': 'Bearer valid-token',
          'content-type': 'application/json'
        },
        body: JSON.stringify({ direction: 'invalid_direction' })
      });

      const response = await POST(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Invalid sync direction');
    });

    it('should handle authentication errors', async () => {
      mockSupabaseAdmin.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Invalid token' }
      });

      const request = new NextRequest('http://localhost/api/domains/domain-123/dns/sync', {
        method: 'POST',
        headers: {
          'authorization': 'Bearer invalid-token',
          'content-type': 'application/json'
        },
        body: JSON.stringify({ direction: 'from_namecheap' })
      });

      const response = await POST(request, { params: { domainId: 'domain-123' } });
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Invalid user');
    });
  });
});
