// src/app/api/namecheap/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// ... (interfaces and constants from your original file remain the same)
interface NamecheapDomainResult {
  Domain: string;
  Available: boolean;
  IsPremiumName: boolean;
  PremiumRegistrationPrice?: number;
  PremiumRenewalPrice?: number;
  PremiumRestorePrice?: number;
  PremiumTransferPrice?: number;
  IcannFee?: number;
  EapFee?: number;
}

interface DomainCheckResult {
  Domain: string;
  Available: boolean;
  IsPremiumName: boolean;
  Price?: number;
  Error?: string;
}

const COMMON_TLDS = [
  { tld: 'com.au', price: 16.50 },
  { tld: 'com', price: 12.98 },
  { tld: 'ai', price: 89.98 },
  { tld: 'net', price: 14.98 },
  { tld: 'org', price: 14.98 },
  { tld: 'net.au', price: 16.50 },
  { tld: 'org.au', price: 16.50 },
  { tld: 'info', price: 18.98 },
  { tld: 'biz', price: 18.98 },
  { tld: 'co', price: 32.98 },
  { tld: 'io', price: 59.98 }
];

const USD_TO_AUD_RATE = 1.5;

// ... (checkDomainAvailability, generateDomainVariations, getPriceForDomain functions are unchanged)
async function checkDomainAvailability(domainList: string[]): Promise<NamecheapDomainResult[]> {
  const apiUser = process.env.NAMECHEAP_API_USER;
  const apiKey = process.env.NAMECHEAP_API_KEY;
  const username = process.env.NAMECHEAP_USERNAME;
  const clientIp = process.env.NAMECHEAP_CLIENT_IP;
  const sandbox = process.env.NAMECHEAP_SANDBOX === 'true';

  if (!apiUser || !apiKey || !username || !clientIp) {
    throw new Error('Missing Namecheap API configuration');
  }

  const baseUrl = sandbox 
    ? 'https://api.sandbox.namecheap.com/xml.response'
    : 'https://api.namecheap.com/xml.response';

  const params = new URLSearchParams({
    ApiUser: apiUser,
    ApiKey: apiKey,
    UserName: username,
    Command: 'namecheap.domains.check',
    ClientIp: clientIp,
    DomainList: domainList.join(',')
  });

  try {
    const response = await fetch(`${baseUrl}?${params.toString()}`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });

    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    const xmlText = await response.text();
    const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\/Error>/);
    if (errorMatch) throw new Error(`Namecheap API error: ${errorMatch[1]}`);

    const domainResults: NamecheapDomainResult[] = [];
    const domainRegex = /<DomainCheckResult[^>]*Domain="([^"]*)"[^>]*Available="([^"]*)"[^>]*IsPremiumName="([^"]*)"[^>]*(?:PremiumRegistrationPrice="([^"]*)")?[^>]*\/>/g;
    let match: RegExpExecArray | null;
    while ((match = domainRegex.exec(xmlText)) !== null) {
      const [, domainName, available, isPremium, premiumPrice] = match;
      const result: NamecheapDomainResult = {
        Domain: domainName,
        Available: available === 'true',
        IsPremiumName: isPremium === 'true',
      };
      if (result.IsPremiumName && premiumPrice) result.PremiumRegistrationPrice = parseFloat(premiumPrice);
      domainResults.push(result);
    }
    return domainResults;
  } catch (error) {
    console.error('Namecheap API error:', error);
    throw error;
  }
}

function generateDomainVariations(baseDomain: string): string[] {
  const domainName = baseDomain.replace(/\.(com|net|org|info|biz|com\.au|net\.au|org\.au|co|io|ai)$/i, '');
  return COMMON_TLDS.map(({ tld }) => `${domainName}.${tld}`);
}

function getPriceForDomain(domain: string, namecheapResult: NamecheapDomainResult): number {
  if (namecheapResult.IsPremiumName && namecheapResult.PremiumRegistrationPrice) {
    return namecheapResult.PremiumRegistrationPrice * USD_TO_AUD_RATE + 5;
  }
  const tld = domain.split('.').slice(1).join('.');
  const tldInfo = COMMON_TLDS.find(t => t.tld === tld);
  if (tldInfo) return tldInfo.price * USD_TO_AUD_RATE + 5;
  return 20 * USD_TO_AUD_RATE + 5;
}

// Helper to format phone number for Namecheap
function formatPhoneNumber(phone: string, country: string): string {
  // Ensure phone is a string
  if (!phone) phone = '';
  if (typeof phone !== 'string') phone = String(phone);
  // If already in +<code>.<number> format, return as is
  if (/^\+\d+\.[\d]+$/.test(phone)) return phone;
  // Map country to country code (expand as needed)
  const countryCodes: Record<string, string> = {
    'IN': '91',
    'IND': '91',
    'India': '91',
    'US': '1',
    'USA': '1',
    'United States': '1',
    // Add more as needed
  };
  let code = countryCodes[country] || '';
  // Try ISO 2-letter code
  if (!code && country.length === 2) code = country.toUpperCase();
  // Fallback: just use the phone as is if no code found
  if (!code) return phone;
  // Remove non-digit characters from phone
  const digits = phone.replace(/[^\d]/g, '');
  return `+${code}.${digits}`;
}

// ✨ NEW FUNCTION: To register a domain with Namecheap
// Accept registrant info as a parameter instead of reading from env
async function registerDomain(domainName: string, registrant: {
  FirstName: string;
  LastName: string;
  Address1: string;
  City: string;
  StateProvince: string;
  PostalCode: string;
  Country: string;
  Phone: string;
  EmailAddress: string;
  // Optionally, allow extra fields for .au domains
  AU_RegistrantIdNumber?: string;
  AU_RegistrantIdType?: string;
}) {
  const apiUser = process.env.NAMECHEAP_API_USER!;
  const apiKey = process.env.NAMECHEAP_API_KEY!;
  const username = process.env.NAMECHEAP_USERNAME!;
  const clientIp = process.env.NAMECHEAP_CLIENT_IP!;
  const sandbox = process.env.NAMECHEAP_SANDBOX === 'true';

  const baseUrl = sandbox 
    ? 'https://api.sandbox.namecheap.com/xml.response' 
    : 'https://api.namecheap.com/xml.response';

  // Validate registrant fields
  for (const [key, value] of Object.entries(registrant)) {
    if (
      [
        'FirstName',
        'LastName',
        'Address1',
        'City',
        'StateProvince',
        'PostalCode',
        'Country',
        'Phone',
        'EmailAddress',
      ].includes(key) && !value
    ) {
      throw new Error(`Missing required registrant field: ${key}`);
    }
  }

  // Format phone number for Namecheap
  const formattedPhone = formatPhoneNumber(registrant.Phone, registrant.Country);

  // ✨ FIX: Correctly map the registrant fields to the required API parameters.
  const params = new URLSearchParams({
    ApiUser: apiUser,
    ApiKey: apiKey,
    UserName: username,
    Command: 'namecheap.domains.create',
    ClientIp: clientIp,
    DomainName: domainName,
    Years: '1',
    // Registrant contact details
    RegistrantFirstName: registrant.FirstName,
    RegistrantLastName: registrant.LastName,
    RegistrantAddress1: registrant.Address1,
    RegistrantCity: registrant.City,
    RegistrantStateProvince: registrant.StateProvince,
    RegistrantPostalCode: registrant.PostalCode,
    RegistrantCountry: registrant.Country,
    RegistrantPhone: formattedPhone,
    RegistrantEmailAddress: registrant.EmailAddress,
    // Admin, Tech, and AuxBilling contacts (using the same details)
    AdminFirstName: registrant.FirstName,
    AdminLastName: registrant.LastName,
    AdminAddress1: registrant.Address1,
    AdminCity: registrant.City,
    AdminStateProvince: registrant.StateProvince,
    AdminPostalCode: registrant.PostalCode,
    AdminCountry: registrant.Country,
    AdminPhone: formattedPhone,
    AdminEmailAddress: registrant.EmailAddress,
    TechFirstName: registrant.FirstName,
    TechLastName: registrant.LastName,
    TechAddress1: registrant.Address1,
    TechCity: registrant.City,
    TechStateProvince: registrant.StateProvince,
    TechPostalCode: registrant.PostalCode,
    TechCountry: registrant.Country,
    TechPhone: formattedPhone,
    TechEmailAddress: registrant.EmailAddress,
    AuxBillingFirstName: registrant.FirstName,
    AuxBillingLastName: registrant.LastName,
    AuxBillingAddress1: registrant.Address1,
    AuxBillingCity: registrant.City,
    AuxBillingStateProvince: registrant.StateProvince,
    AuxBillingPostalCode: registrant.PostalCode,
    AuxBillingCountry: registrant.Country,
    AuxBillingPhone: formattedPhone,
    AuxBillingEmailAddress: registrant.EmailAddress,
  });

  // Add specific parameters for .au domains
  if (domainName.endsWith('.au')) {
    const registrantIdNumber = registrant.AU_RegistrantIdNumber;
    const registrantIdType = registrant.AU_RegistrantIdType;
    if (!registrantIdNumber || !registrantIdType) {
      throw new Error('Missing required fields for .au domain registration: AU_RegistrantIdNumber and AU_RegistrantIdType');
    }
    params.append('au_RegistrantIdNumber', registrantIdNumber);
    params.append('au_RegistrantIdType', registrantIdType);
  }

  try {
    const response = await fetch(`${baseUrl}?${params.toString()}`, { method: 'POST' });
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

    const xmlText = await response.text();
    const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\/Error>/);
    if (errorMatch) {
      throw new Error(`Namecheap API Error: ${errorMatch[1]}`);
    }

    const successMatch = xmlText.match(/<DomainCreateResult[^>]*Registered="true"[^>]*>/);
    if (!successMatch) {
      throw new Error(`Domain registration failed. The response from Namecheap did not confirm success. XML: ${xmlText}`);
    }

    const orderIdMatch = xmlText.match(/OrderID="(\d+)"/);
    const transactionIdMatch = xmlText.match(/TransactionID="(\d+)"/);

    return {
      success: true,
      message: `Domain ${domainName} registered successfully.`,
      orderId: orderIdMatch ? orderIdMatch[1] : null,
      transactionId: transactionIdMatch ? transactionIdMatch[1] : null,
    };
  } catch (error) {
    console.error('Namecheap registration error:', error);
    throw error;
  }
}

// Helper to get Supabase client (service role)
function getSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );
}

// Function to set CNAME record via Namecheap API
async function setCNAMERecord(domain: string, cnameValue: string) {
  const apiUser = process.env.NAMECHEAP_API_USER!;
  const apiKey = process.env.NAMECHEAP_API_KEY!;
  const username = process.env.NAMECHEAP_USERNAME!;
  const clientIp = process.env.NAMECHEAP_CLIENT_IP!;
  const sandbox = process.env.NAMECHEAP_SANDBOX === 'true';
  const baseUrl = sandbox
    ? 'https://api.sandbox.namecheap.com/xml.response'
    : 'https://api.namecheap.com/xml.response';

  // Namecheap API for DNS setHosts
  const params = new URLSearchParams({
    ApiUser: apiUser,
    ApiKey: apiKey,
    UserName: username,
    Command: 'namecheap.domains.dns.setHosts',
    ClientIp: clientIp,
    SLD: domain.split('.')[0],
    TLD: domain.split('.').slice(1).join('.'),
    'HostName1': '@',
    'RecordType1': 'CNAME',
    'Address1': cnameValue,
    'TTL1': '1800',
  });

  const response = await fetch(`${baseUrl}?${params.toString()}`, { method: 'POST' });
  const xmlText = await response.text();
  const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\/Error>/);
  if (errorMatch) {
    throw new Error(`Namecheap DNS Error: ${errorMatch[1]}`);
  }
  const successMatch = xmlText.match(/<DomainDNSSetHostsResult[^>]*IsSuccess="true"/);
  if (!successMatch) {
    throw new Error(`CNAME mapping failed. XML: ${xmlText}`);
  }
  return { success: true, message: `CNAME for ${domain} -> ${cnameValue} set successfully.` };
}

export async function POST(request: NextRequest) {
  try {
    const requestBody = await request.json();
    const { domain, action, siteId, userId, stripeSessionId } = requestBody;

    if (!domain) {
      return NextResponse.json({ error: 'Domain is required' }, { status: 400 });
    }

    if (action === 'check') {
      const domainVariations = generateDomainVariations(domain);
      const namecheapResults = await checkDomainAvailability(domainVariations);
      const results: DomainCheckResult[] = namecheapResults.map(result => {
        const formattedResult: DomainCheckResult = {
          Domain: result.Domain,
          Available: result.Available,
          IsPremiumName: result.IsPremiumName,
        };
        if (result.Available) {
          formattedResult.Price = getPriceForDomain(result.Domain, result);
        }
        return formattedResult;
      });
      return NextResponse.json({ results });
    }

    if (action === 'register') {
      if (!userId) {
        return NextResponse.json({ error: 'userId is required for registration' }, { status: 400 });
      }

      // Extract registrant info from request body
      const registrant = requestBody.registrant;
      if (!registrant) {
        return NextResponse.json({ error: 'Registrant information is required' }, { status: 400 });
      }
      // Validate required fields
      const requiredFields = [
        'FirstName',
        'LastName',
        'Address1',
        'City',
        'StateProvince',
        'PostalCode',
        'Country',
        'Phone',
        'EmailAddress',
      ];
      for (const field of requiredFields) {
        if (!registrant[field]) {
          return NextResponse.json({ error: `Missing registrant field: ${field}` }, { status: 400 });
        }
      }
      // For .au domains, check AU_RegistrantIdNumber and AU_RegistrantIdType
      if (domain.endsWith('.au')) {
        if (!registrant.AU_RegistrantIdNumber || !registrant.AU_RegistrantIdType) {
          return NextResponse.json({ error: 'Missing AU_RegistrantIdNumber or AU_RegistrantIdType for .au domain' }, { status: 400 });
        }
      }

      const supabase = getSupabaseServiceClient();

      // Handle site mapping - if siteId is 'pending', we'll register the domain without site mapping
      let siteName = null;
      let finalSiteId = null;

      if (siteId && siteId !== 'pending') {
        // Fetch site_name from Supabase
        console.log('[Namecheap API] Fetching site_name for siteId:', siteId);
        const { data: site, error: siteError } = await supabase
          .from('user-websites')
          .select('site_name')
          .eq('id', siteId)
          .single();
        if (siteError || !site) {
          console.error('[Namecheap API] Could not fetch site_name for siteId:', siteId, siteError);
          return NextResponse.json({ error: 'Could not fetch site_name for siteId' }, { status: 400 });
        }
        siteName = site.site_name;
        finalSiteId = siteId;
        console.log('[Namecheap API] site_name fetched:', siteName);
      } else {
        console.log('[Namecheap API] Registering domain without site mapping (siteId is pending)');
      }

      // Create or update domain record in database
      const domainData = {
        domain_name: domain,
        user_id: userId,
        site_id: finalSiteId,
        status: 'pending',
        stripe_session_id: stripeSessionId,
        cname_target: siteName,
      };

      // Check if domain record already exists
      const { data: existingDomain } = await supabase
        .from('domains')
        .select('id')
        .eq('domain_name', domain)
        .eq('user_id', userId)
        .single();

      let domainRecord: any;
      if (existingDomain) {
        // Update existing domain record
        const { data, error: updateError } = await supabase
          .from('domains')
          .update({ ...domainData, status: 'pending' })
          .eq('id', existingDomain.id)
          .select()
          .single();

        if (updateError) {
          console.error('[Namecheap API] Failed to update domain record:', updateError);
          return NextResponse.json({ error: 'Failed to update domain record' }, { status: 500 });
        }
        domainRecord = data;
      } else {
        // Create new domain record
        const { data, error: insertError } = await supabase
          .from('domains')
          .insert(domainData)
          .select()
          .single();

        if (insertError) {
          console.error('[Namecheap API] Failed to create domain record:', insertError);
          return NextResponse.json({ error: 'Failed to create domain record' }, { status: 500 });
        }
        domainRecord = data;
      }

      // Register domain with Namecheap
      console.log('[Namecheap API] Registering domain:', domain);
      let registrationResult: any;
      try {
        registrationResult = await registerDomain(domain, registrant);
        console.log('[Namecheap API] Registration result:', registrationResult);

        // Update domain record with registration details
        const registrationDate = new Date().toISOString();
        const expiryDate = new Date();
        expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 1 year from now

        await supabase
          .from('domains')
          .update({
            status: 'registered',
            registration_date: registrationDate,
            expiry_date: expiryDate.toISOString(),
            namecheap_order_id: registrationResult.orderId,
            namecheap_transaction_id: registrationResult.transactionId,
          })
          .eq('id', domainRecord.id);

      } catch (regError) {
        console.error('[Namecheap API] Registration failed:', regError);

        // Update domain record to failed status
        await supabase
          .from('domains')
          .update({ status: 'failed' })
          .eq('id', domainRecord.id);

        return NextResponse.json({
          error: regError instanceof Error ? regError.message : 'Domain registration failed'
        }, { status: 500 });
      }

      // Set CNAME record and update site mapping only if we have a site
      let cnameResult = null;
      let siteNameUpdated = false;

      if (siteName && finalSiteId) {
        try {
          console.log('[Namecheap API] Setting CNAME for domain:', domain, 'to:', siteName);
          cnameResult = await setCNAMERecord(domain, siteName);
          console.log('[Namecheap API] CNAME mapping result:', cnameResult);

          // Update domain record with DNS configuration
          await supabase
            .from('domains')
            .update({
              dns_configured: true,
              status: 'active'
            })
            .eq('id', domainRecord.id);

          // Update site_name to the new domain
          console.log('[Namecheap API] Updating site_name for siteId:', finalSiteId, 'to:', domain);
          const { error: updateError } = await supabase
            .from('user-websites')
            .update({ site_name: domain })
            .eq('id', finalSiteId);
          if (updateError) {
            console.error('[Namecheap API] Failed to update site_name:', updateError);
            return NextResponse.json({
              ...registrationResult,
              cnameResult,
              updateError: updateError.message,
            }, { status: 500 });
          }
          console.log('[Namecheap API] site_name updated successfully.');
          siteNameUpdated = true;

        } catch (cnameErr) {
          console.error('[Namecheap API] CNAME mapping error:', cnameErr);
          return NextResponse.json({
            ...registrationResult,
            cnameError: cnameErr instanceof Error ? cnameErr.message : cnameErr,
          }, { status: 500 });
        }
      } else {
        console.log('[Namecheap API] Skipping CNAME setup - no site mapping provided');
        // Update domain status to registered (not active since no DNS setup)
        await supabase
          .from('domains')
          .update({ status: 'registered' })
          .eq('id', domainRecord.id);
      }

      return NextResponse.json({
        ...registrationResult,
        cnameResult,
        siteNameUpdated,
        domainRecord,
        message: siteName ? 'Domain registered and mapped to site' : 'Domain registered successfully (no site mapping)'
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}