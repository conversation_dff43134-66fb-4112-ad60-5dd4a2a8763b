import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';
import { getOrCreateStripeCustomer, getStripeCustomerIdFromProfile } from '@/lib/stripe-customer';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
  typescript: true,
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

interface SiteBillingData {
  id: string;
  site_name: string;
  subscription_status: 'active' | 'inactive' | 'pending' | 'cancelled' | 'expired';
  plan_name: string;
  amount: number;
  currency: string;
  next_billing_date: string | null;
  created_at: string;
  updated_at: string;
  stripe_subscription_id: string | null;
  stripe_customer_id: string | null;
  site_id: string;
  user_id: string;
  last_invoice_amount?: number;
  last_invoice_date?: string;
  last_invoice_status?: string;
}

// Helper function to map Stripe subscription status to our status
const mapSubscriptionStatus = (status: Stripe.Subscription.Status): SiteBillingData['subscription_status'] => {
  switch (status) {
    case 'active':
      return 'active';
    case 'canceled':
      return 'cancelled';
    case 'incomplete':
    case 'incomplete_expired':
      return 'expired';
    case 'past_due':
    case 'trialing':
      return 'pending';
    default:
      return 'inactive';
  }
};

// Expects: Authorization: Bearer <supabase_jwt>
export async function GET(req: NextRequest) {
  try {
    // 1. Get the Supabase JWT from the request
    const authHeader = req.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // 2. Get the user from the JWT
    const { data: { user }, error } = await supabase.auth.getUser(token);
    if (error || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    // 3. Get the Stripe customer ID from profiles table
    const stripeCustomerId = await getStripeCustomerIdFromProfile(user.email!);

    if (!stripeCustomerId) {
      return NextResponse.json({ error: 'No Stripe customer found' }, { status: 404 });
    }

    // 4. Fetch user's sites
    const { data: sites, error: sitesError } = await supabase
      .from('user-websites')
      .select('id, site_name, created_at, updated_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (sitesError) {
      console.error('Error fetching sites:', sitesError);
      return NextResponse.json({ error: 'Failed to fetch sites' }, { status: 500 });
    }

    // 5. Fetch all subscriptions for the customer
    const subscriptions = await stripe.subscriptions.list({
      customer: stripeCustomerId,
      limit: 100,
      expand: ['data.items.data.price'],
    });

    console.log('subscriptions', subscriptions);

    // 6. Fetch recent invoices for the customer
    const invoices = await stripe.invoices.list({
      customer: stripeCustomerId,
      limit: 50,
    });

    // 7. Create site billing data by matching subscriptions with sites
    const siteBillingData: SiteBillingData[] = sites?.map(site => {
      // Find subscription for this site (using metadata.siteId)
      const siteSubscription = subscriptions.data.find(sub => 
        sub.metadata?.siteId === site.id
      );

      // Find recent invoice for this subscription
      const siteInvoice = siteSubscription 
        ? invoices.data.find(inv => (inv as any).subscription === siteSubscription.id)
        : null;

      return {
        id: site.id,
        site_name: site.site_name,
        subscription_status: siteSubscription ? mapSubscriptionStatus(siteSubscription.status) : 'inactive',
        plan_name: siteSubscription?.items.data[0]?.price.nickname || 
                  (siteSubscription?.items.data[0]?.price.product as Stripe.Product)?.name || 
                  'No Plan',
        amount: siteSubscription?.items.data[0]?.price.unit_amount 
          ? siteSubscription.items.data[0].price.unit_amount / 100 
          : 0,
        currency: siteSubscription?.items.data[0]?.price.currency || 'usd',
        next_billing_date: siteSubscription?.items.data[0]?.current_period_end 
          ? new Date(siteSubscription.items.data[0].current_period_end * 1000).toISOString()
          : null,
        created_at: site.created_at,
        updated_at: site.updated_at,
        stripe_subscription_id: siteSubscription?.id || null,
        stripe_customer_id: stripeCustomerId,
        site_id: site.id,
        user_id: user.id,
        last_invoice_amount: siteInvoice ? siteInvoice.amount_paid / 100 : undefined,
        last_invoice_date: siteInvoice ? new Date(siteInvoice.created * 1000).toISOString() : undefined,
        last_invoice_status: siteInvoice?.status,
      };
    }) || [];

    return NextResponse.json({ 
      siteBillingData,
      totalSites: siteBillingData.length,
      activeSubscriptions: siteBillingData.filter(site => site.subscription_status === 'active').length,
    });

  } catch (error: any) {
    console.error('Site Billing API Error:', error.message);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 