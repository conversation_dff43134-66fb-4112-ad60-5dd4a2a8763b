// Namecheap DNS API Service
// This module handles all DNS-related operations with the Namecheap API

// Retry configuration
interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffMultiplier: 2
};

// Custom error types
export class NamecheapAPIError extends Error {
  constructor(
    message: string,
    public code?: string,
    public isRetryable: boolean = false
  ) {
    super(message);
    this.name = 'NamecheapAPIError';
  }
}

export class NamecheapRateLimitError extends NamecheapAPIError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 'RATE_LIMIT', true);
    this.name = 'NamecheapRateLimitError';
  }
}

export class NamecheapNetworkError extends NamecheapAPIError {
  constructor(message: string = 'Network error') {
    super(message, 'NETWORK_ERROR', true);
    this.name = 'NamecheapNetworkError';
  }
}

// Sleep utility for retry delays
const sleep = (ms: number): Promise<void> =>
  new Promise(resolve => setTimeout(resolve, ms));

// Circuit breaker implementation
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private failureThreshold: number = 5,
    private recoveryTimeout: number = 60000 // 1 minute
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new NamecheapAPIError('Circuit breaker is OPEN - service temporarily unavailable', 'CIRCUIT_BREAKER_OPEN', true);
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess() {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  private onFailure() {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
    }
  }

  getState() {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime
    };
  }
}

// Global circuit breaker instance
const namecheapCircuitBreaker = new CircuitBreaker();

// Calculate exponential backoff delay
const calculateDelay = (attempt: number, config: RetryConfig): number => {
  const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt);
  return Math.min(delay, config.maxDelay);
};

// Retry wrapper function
async function withRetry<T>(
  operation: () => Promise<T>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG,
  context: string = 'operation'
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;

      // Don't retry if it's the last attempt
      if (attempt === config.maxRetries) {
        break;
      }

      // Check if error is retryable
      const isRetryable = error instanceof NamecheapAPIError
        ? error.isRetryable
        : error instanceof Error && (
            error.message.includes('timeout') ||
            error.message.includes('network') ||
            error.message.includes('ECONNRESET') ||
            error.message.includes('ENOTFOUND') ||
            error.message.includes('fetch')
          );

      if (!isRetryable) {
        console.log(`${context}: Error is not retryable, failing immediately:`, error.message);
        break;
      }

      const delay = calculateDelay(attempt, config);
      console.log(`${context}: Attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error.message);
      await sleep(delay);
    }
  }

  throw lastError;
}

// Interface for DNS record structure used by Namecheap API
export interface NamecheapDNSRecord {
  HostName: string;
  RecordType: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'NS' | 'PTR' | 'SRV' | 'CAA';
  Address: string;
  MXPref?: string;
  TTL: string;
  HostId?: string;
}

// Interface for our internal DNS record structure
export interface DNSRecord {
  id?: string;
  domain_id?: string;
  type: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'NS' | 'PTR' | 'SRV' | 'CAA';
  name: string;
  value: string;
  ttl: number;
  priority?: number;
  status?: 'active' | 'pending' | 'error';
}

// Configuration interface for Namecheap API
interface NamecheapConfig {
  apiUser: string;
  apiKey: string;
  username: string;
  clientIp: string;
  sandbox: boolean;
}

// Get Namecheap API configuration from environment variables
function getNamecheapConfig(): NamecheapConfig {
  const apiUser = process.env.NAMECHEAP_API_USER;
  const apiKey = process.env.NAMECHEAP_API_KEY;
  const username = process.env.NAMECHEAP_USERNAME;
  const clientIp = process.env.NAMECHEAP_CLIENT_IP;
  const sandbox = process.env.NAMECHEAP_SANDBOX === 'true';

  if (!apiUser || !apiKey || !username || !clientIp) {
    throw new Error('Missing Namecheap API configuration. Please check environment variables.');
  }

  return { apiUser, apiKey, username, clientIp, sandbox };
}

// Get the appropriate Namecheap API base URL
function getApiBaseUrl(sandbox: boolean): string {
  return sandbox 
    ? 'https://api.sandbox.namecheap.com/xml.response'
    : 'https://api.namecheap.com/xml.response';
}

// Parse domain into SLD and TLD components
function parseDomain(domain: string): { sld: string; tld: string } {
  const parts = domain.split('.');
  if (parts.length < 2) {
    throw new Error(`Invalid domain format: ${domain}`);
  }
  
  return {
    sld: parts[0],
    tld: parts.slice(1).join('.')
  };
}

// Function to get current DNS records from Namecheap
export async function getNamecheapDNSRecords(domain: string): Promise<NamecheapDNSRecord[]> {
  return namecheapCircuitBreaker.execute(() => withRetry(async () => {
    const config = getNamecheapConfig();
    const baseUrl = getApiBaseUrl(config.sandbox);
    const { sld, tld } = parseDomain(domain);

    const params = new URLSearchParams({
      ApiUser: config.apiUser,
      ApiKey: config.apiKey,
      UserName: config.username,
      Command: 'namecheap.domains.dns.getHosts',
      ClientIp: config.clientIp,
      SLD: sld,
      TLD: tld,
    });

    let response: Response;
    try {
      response = await fetch(`${baseUrl}?${params.toString()}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 30000 // 30 second timeout
      });
    } catch (fetchError: any) {
      if (fetchError.name === 'AbortError' || fetchError.message.includes('timeout')) {
        throw new NamecheapNetworkError('Request timeout');
      }
      throw new NamecheapNetworkError(`Network error: ${fetchError.message}`);
    }

    if (!response.ok) {
      if (response.status === 429) {
        throw new NamecheapRateLimitError();
      }
      if (response.status >= 500) {
        throw new NamecheapAPIError(`Server error: ${response.status}`, 'SERVER_ERROR', true);
      }
      throw new NamecheapAPIError(`HTTP error: ${response.status}`, 'HTTP_ERROR', false);
    }

    const xmlText = await response.text();
    console.log('Namecheap getHosts response:', xmlText);

    // Check for API errors
    const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\/Error>/);
    if (errorMatch) {
      const errorMessage = errorMatch[1];

      // Determine if error is retryable
      const isRetryable = errorMessage.includes('timeout') ||
                         errorMessage.includes('temporarily unavailable') ||
                         errorMessage.includes('server error');

      throw new NamecheapAPIError(`Namecheap DNS API Error: ${errorMessage}`, 'API_ERROR', isRetryable);
    }

    // Parse DNS records from XML response
    const records: NamecheapDNSRecord[] = [];
    const hostRegex = /<Host[^>]*HostId="([^"]*)"[^>]*Name="([^"]*)"[^>]*Type="([^"]*)"[^>]*Address="([^"]*)"[^>]*(?:MXPref="([^"]*)")?[^>]*TTL="([^"]*)"[^>]*\/>/g;

    let match;
    while ((match = hostRegex.exec(xmlText)) !== null) {
      const [, hostId, name, type, address, mxPref, ttl] = match;
      records.push({
        HostId: hostId,
        HostName: name,
        RecordType: type as NamecheapDNSRecord['RecordType'],
        Address: address,
        MXPref: mxPref || undefined,
        TTL: ttl,
      });
    }

    console.log(`Retrieved ${records.length} DNS records from Namecheap for ${domain}`);
    return records;
  }, DEFAULT_RETRY_CONFIG, `getNamecheapDNSRecords(${domain})`));
}

// Function to set DNS records on Namecheap
export async function setNamecheapDNSRecords(domain: string, records: DNSRecord[]): Promise<{ success: boolean; message: string }> {
  return namecheapCircuitBreaker.execute(() => withRetry(async () => {
    const config = getNamecheapConfig();
    const baseUrl = getApiBaseUrl(config.sandbox);
    const { sld, tld } = parseDomain(domain);

    if (records.length === 0) {
      throw new NamecheapAPIError('At least one DNS record is required', 'VALIDATION_ERROR', false);
    }

    if (records.length > 100) {
      throw new NamecheapAPIError('Maximum 100 DNS records allowed per request', 'VALIDATION_ERROR', false);
    }

    const params = new URLSearchParams({
      ApiUser: config.apiUser,
      ApiKey: config.apiKey,
      UserName: config.username,
      Command: 'namecheap.domains.dns.setHosts',
      ClientIp: config.clientIp,
      SLD: sld,
      TLD: tld,
    });

    // Add each DNS record to the parameters
    records.forEach((record, index) => {
      const recordIndex = index + 1;
      params.append(`HostName${recordIndex}`, record.name || '@');
      params.append(`RecordType${recordIndex}`, record.type);
      params.append(`Address${recordIndex}`, record.value);
      params.append(`TTL${recordIndex}`, record.ttl.toString());

      // Add MX priority if it's an MX record
      if (record.type === 'MX' && record.priority !== undefined) {
        params.append(`MXPref${recordIndex}`, record.priority.toString());
      }
    });

    console.log(`Setting ${records.length} DNS records on Namecheap for ${domain}`);

    let response: Response;
    try {
      response = await fetch(`${baseUrl}?${params.toString()}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 30000 // 30 second timeout
      });
    } catch (fetchError: any) {
      if (fetchError.name === 'AbortError' || fetchError.message.includes('timeout')) {
        throw new NamecheapNetworkError('Request timeout');
      }
      throw new NamecheapNetworkError(`Network error: ${fetchError.message}`);
    }

    if (!response.ok) {
      if (response.status === 429) {
        throw new NamecheapRateLimitError();
      }
      if (response.status >= 500) {
        throw new NamecheapAPIError(`Server error: ${response.status}`, 'SERVER_ERROR', true);
      }
      throw new NamecheapAPIError(`HTTP error: ${response.status}`, 'HTTP_ERROR', false);
    }

    const xmlText = await response.text();
    console.log('Namecheap setHosts response:', xmlText);

    // Check for API errors
    const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\/Error>/);
    if (errorMatch) {
      const errorMessage = errorMatch[1];

      // Determine if error is retryable
      const isRetryable = errorMessage.includes('timeout') ||
                         errorMessage.includes('temporarily unavailable') ||
                         errorMessage.includes('server error') ||
                         errorMessage.includes('try again');

      throw new NamecheapAPIError(`Namecheap DNS API Error: ${errorMessage}`, 'API_ERROR', isRetryable);
    }

    const successMatch = xmlText.match(/<DomainDNSSetHostsResult[^>]*IsSuccess="true"/);
    if (!successMatch) {
      throw new NamecheapAPIError(`DNS records update failed. XML: ${xmlText}`, 'UPDATE_FAILED', false);
    }

    console.log(`Successfully updated DNS records on Namecheap for ${domain}`);
    return { success: true, message: `DNS records updated successfully for ${domain}` };
  }, DEFAULT_RETRY_CONFIG, `setNamecheapDNSRecords(${domain})`));
}

// Convert Namecheap DNS record to our internal format
export function convertNamecheapToInternal(namecheapRecord: NamecheapDNSRecord): Omit<DNSRecord, 'id' | 'domain_id'> {
  return {
    type: namecheapRecord.RecordType,
    name: namecheapRecord.HostName === '@' ? '' : namecheapRecord.HostName,
    value: namecheapRecord.Address,
    ttl: parseInt(namecheapRecord.TTL),
    priority: namecheapRecord.MXPref ? parseInt(namecheapRecord.MXPref) : undefined,
    status: 'active'
  };
}

// Convert our internal DNS record to Namecheap format
export function convertInternalToNamecheap(internalRecord: DNSRecord): Omit<NamecheapDNSRecord, 'HostId'> {
  return {
    HostName: internalRecord.name || '@',
    RecordType: internalRecord.type,
    Address: internalRecord.value,
    TTL: internalRecord.ttl.toString(),
    MXPref: internalRecord.priority?.toString()
  };
}

// Validate DNS record before sending to Namecheap
export function validateDNSRecord(record: DNSRecord): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate record type
  const validTypes = ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS', 'PTR', 'SRV', 'CAA'];
  if (!validTypes.includes(record.type)) {
    errors.push(`Invalid record type: ${record.type}`);
  }

  // Validate TTL
  if (record.ttl < 60 || record.ttl > 60000) {
    errors.push('TTL must be between 60 and 60000 seconds');
  }

  // Validate MX record priority
  if (record.type === 'MX') {
    if (record.priority === undefined || record.priority < 0 || record.priority > 65535) {
      errors.push('MX records must have a priority between 0 and 65535');
    }
  }

  // Validate record value is not empty
  if (!record.value || record.value.trim() === '') {
    errors.push('Record value cannot be empty');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

// Get circuit breaker status for monitoring
export function getCircuitBreakerStatus() {
  return namecheapCircuitBreaker.getState();
}

// Reset circuit breaker (for administrative purposes)
export function resetCircuitBreaker() {
  const newBreaker = new CircuitBreaker();
  Object.setPrototypeOf(namecheapCircuitBreaker, newBreaker);
  Object.assign(namecheapCircuitBreaker, newBreaker);
}
