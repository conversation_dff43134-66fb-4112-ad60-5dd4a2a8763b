import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import {
  getNamecheapDNSRecords,
  setNamecheapDNSRecords,
  validateDNSRecord,
  convertNamecheapToInternal,
  convertInternalToNamecheap,
  NamecheapAPIError,
  NamecheapRateLimitError,
  NamecheapNetworkError,
  getCircuitBreakerStatus,
  resetCircuitBreaker,
  type DNSRecord,
  type NamecheapDNSRecord
} from '../namecheap-dns';

// Mock fetch globally
global.fetch = jest.fn();

describe('Namecheap DNS API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    resetCircuitBreaker();
    
    // Mock environment variables
    process.env.NAMECHEAP_API_USER = 'test-user';
    process.env.NAMECHEAP_API_KEY = 'test-key';
    process.env.NAMECHEAP_USERNAME = 'test-username';
    process.env.NAMECHEAP_CLIENT_IP = '***********';
    process.env.NAMECHEAP_SANDBOX = 'true';
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getNamecheapDNSRecords', () => {
    it('should successfully fetch DNS records', async () => {
      const mockResponse = `
        <?xml version="1.0" encoding="UTF-8"?>
        <ApiResponse Status="OK">
          <CommandResponse Type="namecheap.domains.dns.getHosts">
            <DomainDNSGetHostsResult Domain="example.com" IsUsingOurDNS="true">
              <Host HostId="1" Name="@" Type="A" Address="***********" MXPref="10" TTL="3600" />
              <Host HostId="2" Name="www" Type="CNAME" Address="example.com" MXPref="10" TTL="3600" />
            </DomainDNSGetHostsResult>
          </CommandResponse>
        </ApiResponse>
      `;

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(mockResponse),
      } as Response);

      const records = await getNamecheapDNSRecords('example.com');

      expect(records).toHaveLength(2);
      expect(records[0]).toEqual({
        HostId: '1',
        HostName: '@',
        RecordType: 'A',
        Address: '***********',
        MXPref: '10',
        TTL: '3600'
      });
      expect(records[1]).toEqual({
        HostId: '2',
        HostName: 'www',
        RecordType: 'CNAME',
        Address: 'example.com',
        MXPref: '10',
        TTL: '3600'
      });
    });

    it('should handle API errors', async () => {
      const mockResponse = `
        <?xml version="1.0" encoding="UTF-8"?>
        <ApiResponse Status="ERROR">
          <Errors>
            <Error Number="2019166">Domain not found</Error>
          </Errors>
        </ApiResponse>
      `;

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(mockResponse),
      } as Response);

      await expect(getNamecheapDNSRecords('nonexistent.com')).rejects.toThrow(
        'Namecheap DNS API Error: Domain not found'
      );
    });

    it('should handle network errors with retry', async () => {
      (fetch as jest.MockedFunction<typeof fetch>)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve(`
            <?xml version="1.0" encoding="UTF-8"?>
            <ApiResponse Status="OK">
              <CommandResponse Type="namecheap.domains.dns.getHosts">
                <DomainDNSGetHostsResult Domain="example.com" IsUsingOurDNS="true">
                </DomainDNSGetHostsResult>
              </CommandResponse>
            </ApiResponse>
          `),
        } as Response);

      const records = await getNamecheapDNSRecords('example.com');
      expect(records).toHaveLength(0);
      expect(fetch).toHaveBeenCalledTimes(3);
    });

    it('should handle rate limiting', async () => {
      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: false,
        status: 429,
      } as Response);

      await expect(getNamecheapDNSRecords('example.com')).rejects.toThrow(NamecheapRateLimitError);
    });
  });

  describe('setNamecheapDNSRecords', () => {
    const testRecords: DNSRecord[] = [
      {
        type: 'A',
        name: '@',
        value: '***********',
        ttl: 3600
      },
      {
        type: 'MX',
        name: '@',
        value: 'mail.example.com',
        ttl: 3600,
        priority: 10
      }
    ];

    it('should successfully set DNS records', async () => {
      const mockResponse = `
        <?xml version="1.0" encoding="UTF-8"?>
        <ApiResponse Status="OK">
          <CommandResponse Type="namecheap.domains.dns.setHosts">
            <DomainDNSSetHostsResult Domain="example.com" IsSuccess="true" />
          </CommandResponse>
        </ApiResponse>
      `;

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(mockResponse),
      } as Response);

      const result = await setNamecheapDNSRecords('example.com', testRecords);

      expect(result).toEqual({
        success: true,
        message: 'DNS records updated successfully for example.com'
      });
    });

    it('should validate input records', async () => {
      await expect(setNamecheapDNSRecords('example.com', [])).rejects.toThrow(
        'At least one DNS record is required'
      );

      const tooManyRecords = Array(101).fill(testRecords[0]);
      await expect(setNamecheapDNSRecords('example.com', tooManyRecords)).rejects.toThrow(
        'Maximum 100 DNS records allowed per request'
      );
    });

    it('should handle server errors with retry', async () => {
      (fetch as jest.MockedFunction<typeof fetch>)
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve(`
            <?xml version="1.0" encoding="UTF-8"?>
            <ApiResponse Status="OK">
              <CommandResponse Type="namecheap.domains.dns.setHosts">
                <DomainDNSSetHostsResult Domain="example.com" IsSuccess="true" />
              </CommandResponse>
            </ApiResponse>
          `),
        } as Response);

      const result = await setNamecheapDNSRecords('example.com', testRecords);
      expect(result.success).toBe(true);
      expect(fetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('validateDNSRecord', () => {
    it('should validate valid DNS records', () => {
      const validRecord: DNSRecord = {
        type: 'A',
        name: 'www',
        value: '***********',
        ttl: 3600
      };

      const result = validateDNSRecord(validRecord);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate MX records with priority', () => {
      const validMXRecord: DNSRecord = {
        type: 'MX',
        name: '@',
        value: 'mail.example.com',
        ttl: 3600,
        priority: 10
      };

      const result = validateDNSRecord(validMXRecord);
      expect(result.valid).toBe(true);

      const invalidMXRecord: DNSRecord = {
        type: 'MX',
        name: '@',
        value: 'mail.example.com',
        ttl: 3600
        // Missing priority
      };

      const invalidResult = validateDNSRecord(invalidMXRecord);
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors).toContain('MX records must have a priority between 0 and 65535');
    });

    it('should validate TTL values', () => {
      const invalidTTLRecord: DNSRecord = {
        type: 'A',
        name: 'www',
        value: '***********',
        ttl: 30 // Too low
      };

      const result = validateDNSRecord(invalidTTLRecord);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('TTL must be between 60 and 60000 seconds');
    });

    it('should validate empty values', () => {
      const emptyValueRecord: DNSRecord = {
        type: 'A',
        name: 'www',
        value: '',
        ttl: 3600
      };

      const result = validateDNSRecord(emptyValueRecord);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Record value cannot be empty');
    });
  });

  describe('convertNamecheapToInternal', () => {
    it('should convert Namecheap record to internal format', () => {
      const namecheapRecord: NamecheapDNSRecord = {
        HostId: '1',
        HostName: 'www',
        RecordType: 'A',
        Address: '***********',
        TTL: '3600'
      };

      const result = convertNamecheapToInternal(namecheapRecord);
      expect(result).toEqual({
        type: 'A',
        name: 'www',
        value: '***********',
        ttl: 3600,
        priority: undefined,
        status: 'active'
      });
    });

    it('should handle @ hostname', () => {
      const namecheapRecord: NamecheapDNSRecord = {
        HostId: '1',
        HostName: '@',
        RecordType: 'A',
        Address: '***********',
        TTL: '3600'
      };

      const result = convertNamecheapToInternal(namecheapRecord);
      expect(result.name).toBe('');
    });

    it('should handle MX records with priority', () => {
      const namecheapRecord: NamecheapDNSRecord = {
        HostId: '1',
        HostName: '@',
        RecordType: 'MX',
        Address: 'mail.example.com',
        TTL: '3600',
        MXPref: '10'
      };

      const result = convertNamecheapToInternal(namecheapRecord);
      expect(result.priority).toBe(10);
    });
  });

  describe('convertInternalToNamecheap', () => {
    it('should convert internal record to Namecheap format', () => {
      const internalRecord: DNSRecord = {
        type: 'A',
        name: 'www',
        value: '***********',
        ttl: 3600
      };

      const result = convertInternalToNamecheap(internalRecord);
      expect(result).toEqual({
        HostName: 'www',
        RecordType: 'A',
        Address: '***********',
        TTL: '3600',
        MXPref: undefined
      });
    });

    it('should handle empty name as @', () => {
      const internalRecord: DNSRecord = {
        type: 'A',
        name: '',
        value: '***********',
        ttl: 3600
      };

      const result = convertInternalToNamecheap(internalRecord);
      expect(result.HostName).toBe('@');
    });

    it('should handle MX records with priority', () => {
      const internalRecord: DNSRecord = {
        type: 'MX',
        name: '@',
        value: 'mail.example.com',
        ttl: 3600,
        priority: 10
      };

      const result = convertInternalToNamecheap(internalRecord);
      expect(result.MXPref).toBe('10');
    });
  });

  describe('Circuit Breaker', () => {
    it('should track circuit breaker state', () => {
      const status = getCircuitBreakerStatus();
      expect(status.state).toBe('CLOSED');
      expect(status.failures).toBe(0);
    });

    it('should open circuit after multiple failures', async () => {
      // Mock multiple failures
      (fetch as jest.MockedFunction<typeof fetch>).mockRejectedValue(new Error('Network error'));

      // Try multiple times to trigger circuit breaker
      for (let i = 0; i < 6; i++) {
        try {
          await getNamecheapDNSRecords('example.com');
        } catch (error) {
          // Expected to fail
        }
      }

      const status = getCircuitBreakerStatus();
      expect(status.state).toBe('OPEN');
      expect(status.failures).toBeGreaterThanOrEqual(5);
    });
  });
});
